#!/usr/bin/env python3
"""
Haptic VR Glove - Professional CAD Blueprint Renderer
Using CadQuery for full parametric CAD functionality
Based on OpenSCAD specifications from the bill of materials
"""

import cadquery as cq
from ocp_vscode import show, set_port, set_defaults
import math

# Global parameters from OpenSCAD spec
MOTOR_LEN = 26.0  # mm
MOTOR_W = 10.0    # mm
MOTOR_H = 12.0    # mm
GEAR_OD = 6.0     # mm
GEAR_TH = 2.0     # mm
SHAFT_BORE = 3.05 # mm
BEARING_OD = 8.0  # mm
BEARING_H = 2.5   # mm
FINGER_SPACING = 22.0  # mm

# Set up the native viewer defaults
set_defaults(
    axes=True,
    grid=True,
    ortho=False,
    transparent=False,
    theme="light"
)

class HapticGloveCAD:
    """Professional CAD implementation of the Haptic VR Glove using native CadQuery viewer"""
    
    def __init__(self):
        self.components = {}
        
    def create_n20_motor(self):
        """Create N20 6V 298:1 gear motor"""
        # Main motor body (rectangular)
        motor_body = (
            cq.Workplane("XY")
            .rect(MOTOR_LEN, MOTOR_W)
            .extrude(MOTOR_H)
        )
        
        # Motor shaft (D-shaft simulation with cylinder + flat)
        shaft_dia = 3.0
        shaft_len = 10.0
        
        shaft = (
            cq.Workplane("YZ")
            .circle(shaft_dia/2)
            .extrude(shaft_len)
            .translate((MOTOR_LEN/2 + shaft_len/2, 0, MOTOR_H/2))
        )
        
        # D-shaft flat
        flat_cut = (
            cq.Workplane("YZ")
            .rect(shaft_dia, shaft_dia/4)
            .extrude(shaft_len)
            .translate((MOTOR_LEN/2 + shaft_len/2, 0, MOTOR_H/2 + shaft_dia/4))
        )
        
        shaft = shaft.cut(flat_cut)
        
        # Combine motor body and shaft
        motor = motor_body.union(shaft)
        
        # Add mounting holes
        motor = (
            motor.faces(">Z").workplane()
            .rect(MOTOR_LEN-4, MOTOR_W-4, forConstruction=True)
            .vertices()
            .circle(1.0)
            .cutThruAll()
        )
        
        return motor.translate((0, 0, MOTOR_H/2))
    
    def create_spur_gear(self):
        """Create module 0.5, 10-tooth brass spur gear"""
        # Simplified gear representation (professional gear tooth profile would be complex)
        gear_body = (
            cq.Workplane("XY")
            .circle(GEAR_OD/2)
            .extrude(GEAR_TH)
        )
        
        # Central bore for D-shaft
        bore = (
            cq.Workplane("XY")
            .circle(SHAFT_BORE/2)
            .extrude(GEAR_TH + 0.2)
        )
        
        # D-shaft flat in bore
        flat = (
            cq.Workplane("XY")
            .rect(SHAFT_BORE, SHAFT_BORE/4)
            .extrude(GEAR_TH + 0.2)
            .translate((0, SHAFT_BORE/4, 0))
        )
        
        gear = gear_body.cut(bore).cut(flat)
        
        # Add gear teeth (simplified representation)
        tooth_count = 10
        for i in range(tooth_count):
            angle = i * 360 / tooth_count
            tooth = (
                cq.Workplane("XY")
                .rect(0.8, 1.0)
                .extrude(GEAR_TH)
                .rotate((0, 0, 1), (0, 0, 0), angle)
                .translate((GEAR_OD/2 + 0.4, 0, 0))
            )
            gear = gear.union(tooth)
        
        return gear.translate((0, 0, GEAR_TH/2))
    
    def create_bearing(self):
        """Create MR85-2RS bearing (5×8×2.5mm)"""
        # Outer race
        outer_race = (
            cq.Workplane("XY")
            .circle(BEARING_OD/2)
            .circle(5.0/2)  # Inner diameter
            .extrude(BEARING_H)
        )
        
        # Add bearing details (seals, balls representation)
        seal_groove = (
            cq.Workplane("XY")
            .circle(BEARING_OD/2 - 0.5)
            .circle(5.0/2 + 0.5)
            .extrude(0.3)
            .translate((0, 0, BEARING_H - 0.15))
        )
        
        bearing = outer_race.cut(seal_groove)
        
        return bearing.translate((0, 0, BEARING_H/2))
    
    def create_output_drum(self):
        """Create output drum with integrated bearing collar"""
        drum_od = GEAR_OD + 4
        
        # Main drum body
        drum = (
            cq.Workplane("XY")
            .circle(drum_od/2)
            .extrude(GEAR_TH + BEARING_H)
        )
        
        # Bearing pocket
        bearing_pocket = (
            cq.Workplane("XY")
            .circle(BEARING_OD/2)
            .extrude(BEARING_H + 0.1)
            .translate((0, 0, GEAR_TH))
        )
        
        # Central shaft hole
        shaft_hole = (
            cq.Workplane("XY")
            .circle(SHAFT_BORE/2)
            .extrude(GEAR_TH + 0.1)
        )
        
        drum = drum.cut(bearing_pocket).cut(shaft_hole)
        
        # Cable attachment point
        cable_anchor = (
            cq.Workplane("XY")
            .circle(0.5)
            .extrude(2.0)
            .translate((drum_od/2 - 1, 0, (GEAR_TH + BEARING_H)/2))
        )
        
        drum = drum.union(cable_anchor)
        
        return drum.translate((0, 0, (GEAR_TH + BEARING_H)/2))
    

    
    def create_full_glove_assembly(self):
        """Create the complete 5-finger haptic glove assembly"""
        print("🔧 Building complete haptic glove assembly...")
        
        # Create individual components with proper positioning
        all_components = []
        component_names = []
        
        # Add 5 finger units with proper spacing
        for i in range(5):
            print(f"✅ Creating finger unit {i+1}/5...")
            
            # Create components for this finger with proper positioning
            motor = self.create_n20_motor().translate((0, i * FINGER_SPACING, 0))
            gear = self.create_spur_gear().translate((MOTOR_LEN/2 + GEAR_TH/2, i * FINGER_SPACING, MOTOR_H/2))
            bearing = self.create_bearing().translate((MOTOR_LEN/2 + GEAR_TH/2, i * FINGER_SPACING, MOTOR_H/2 + BEARING_H/2))
            drum = self.create_output_drum().translate((MOTOR_LEN/2 + GEAR_TH/2, i * FINGER_SPACING, MOTOR_H/2))
            
            all_components.extend([motor, gear, bearing, drum])
            component_names.extend([
                f"Motor_{i+1}",
                f"Gear_{i+1}", 
                f"Bearing_{i+1}",
                f"Drum_{i+1}"
            ])
        
        # Add back-of-hand PCB
        print("✅ Adding back-of-hand PCB...")
        pcb_size = (50, 30, 1.6)
        pcb = (
            cq.Workplane("XY")
            .rect(pcb_size[0], pcb_size[1])
            .extrude(pcb_size[2])
            .translate((MOTOR_LEN + 10, 2 * FINGER_SPACING, 15))
        )
        
        # Add ESP32 outline on PCB
        esp32_outline = (
            cq.Workplane("XY")
            .rect(25, 18)
            .extrude(3)
            .translate((MOTOR_LEN + 10, 2 * FINGER_SPACING, 15 + pcb_size[2] + 1.5))
        )
        pcb = pcb.union(esp32_outline)
        
        all_components.append(pcb)
        component_names.append("PCB_Assembly")
        
        # Store components for viewing
        self.components = dict(zip(component_names, all_components))
        
        print("🎯 Assembly complete!")
        return all_components, component_names
    
    def show_assembly(self):
        """Display the complete assembly using CadQuery's native viewer"""
        print("🎯 Launching native CadQuery 3D viewer...")
        print("💡 Use the native viewer controls to rotate, zoom, and inspect components")
        print("🔍 All axes, dimensions, and working parts are fully interactive")
        
        # Get the assembly components
        components, names = self.create_full_glove_assembly()
        
        # Use the native show() function to display all components
        # This will automatically start the OCP viewer
        show(*components, names=names, 
             axes=True, 
             grid=True, 
             transparent=False,
             theme="light")
        
        print("✅ Native CadQuery viewer launched!")
        print("🌐 The viewer is now running on http://localhost:3939")
        print("📐 All components are positioned with correct mechanical relationships")
        
        return components

def main():
    """Main execution function"""
    print("🔧 Initializing Professional Haptic VR Glove CAD Renderer...")
    print("📐 Motor specs: {}×{}×{} mm".format(MOTOR_LEN, MOTOR_W, MOTOR_H))
    print("⚙️  Gear specs: Ø{} mm × {} mm thick".format(GEAR_OD, GEAR_TH))
    print("🔄 Bearing specs: Ø{} mm × {} mm thick".format(BEARING_OD, BEARING_H))
    print("📏 Finger spacing: {} mm".format(FINGER_SPACING))
    
    # Create the CAD renderer
    cad_renderer = HapticGloveCAD()
    
    print("🎨 Starting native CadQuery viewer with full assembly...")
    print("🌐 The viewer will launch automatically on http://localhost:3939")
    print("💡 Use mouse controls to rotate, zoom, and inspect all components")
    print("🔍 All working parts, axes, and dimensions are fully interactive")
    
    # Launch the native viewer with the complete assembly
    components = cad_renderer.show_assembly()
    
    print(f"\n✅ SUCCESS! Haptic VR Glove assembly displayed with {len(components)} components")
    print("📐 All components positioned with correct mechanical relationships")
    print("🔧 Based on your OpenSCAD specifications and bill of materials")
    
    return components

if __name__ == "__main__":
    components = main() 